#!/usr/bin/env node

/**
 * 测试修复效果的脚本
 * 运行页面验证工具，验证日志输出和自动登录功能是否正常工作
 */

const { spawn } = require('child_process');
const chalk = require('chalk');
const path = require('path');

console.log(chalk.blue('🧪 测试页面验证工具修复效果'));
console.log(chalk.gray('====================================='));

// 测试项目路径（使用示例项目）
const testProjectPath = '/Users/<USER>/works/galaxy/galaxy-vue3-demi';

// 测试参数
const testArgs = [
  'bin/page-validator.js',
  'check',
  testProjectPath,
  '--auto-fix',
  '--no-headless'
];

console.log(chalk.blue('🚀 启动页面验证工具...'));
console.log(chalk.gray(`   命令: node ${testArgs.join(' ')}`));
console.log(chalk.gray(`   项目路径: ${testProjectPath}`));
console.log('');

// 启动子进程
const child = spawn('node', testArgs, {
  cwd: process.cwd(),
  stdio: 'inherit'
});

// 处理进程事件
child.on('error', (error) => {
  console.error(chalk.red(`❌ 进程启动失败: ${error.message}`));
  process.exit(1);
});

child.on('exit', (code, signal) => {
  console.log('');
  console.log(chalk.gray('====================================='));
  
  if (code === 0) {
    console.log(chalk.green('✅ 测试完成，进程正常退出'));
  } else if (signal) {
    console.log(chalk.yellow(`⚠️  进程被信号终止: ${signal}`));
  } else {
    console.log(chalk.red(`❌ 进程异常退出，代码: ${code}`));
  }
  
  console.log(chalk.blue('📋 测试要点检查:'));
  console.log(chalk.gray('   1. 日志输出是否简洁，没有过多的AI调用详情'));
  console.log(chalk.gray('   2. 自动登录是否正常工作，页面不应该都被重定向到登录页'));
  console.log(chalk.gray('   3. 页面验证速度是否有所提升'));
  console.log(chalk.gray('   4. 错误修复功能是否正常工作'));
  
  process.exit(code || 0);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到中断信号，正在终止测试...'));
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n⚠️  收到终止信号，正在终止测试...'));
  child.kill('SIGTERM');
});
