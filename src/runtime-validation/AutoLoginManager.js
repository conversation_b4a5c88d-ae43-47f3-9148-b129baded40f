const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * AutoLoginManager - 自动登录管理器
 * 
 * 功能：
 * 1. 支持用户名/密码参数传入
 * 2. 默认使用现有登录逻辑
 * 3. 失败时调用 AI 生成新的登录逻辑
 * 4. 使用 JSON 存储 AI 生成的登录配置
 * 5. 支持 Puppeteer 执行登录操作
 */
class AutoLoginManager {
  constructor(options = {}) {
    this.options = {
      username: 'admin',
      password: '111111',
      verbose: false,
      aiEnabled: true,
      configPath: null, // 自动生成配置文件路径
      maxRetries: 3,
      ...options
    };

    // 设置配置文件路径
    if (!this.options.configPath) {
      this.options.configPath = path.join(process.cwd(), '.login-config.json');
    }

    this.loginConfig = null;
    this.isLoggedIn = false;
    this.aiService = null;
  }

  /**
   * 初始化 AI 服务
   */
  async initializeAI() {
    if (!this.options.aiEnabled) {
      return;
    }

    try {
      // 使用 GLM API
      const { AIService } = require('../ai/ai-service');
      this.aiService = new AIService({
        apiKey: process.env.GLM_API_KEY || '3478f0139ba336ca31fc802594b6832c.DV6r88Fm5G2gjbUb',
        model: 'glm-4-flash',
        verbose: this.options.verbose
      });

      if (this.options.verbose) {
        console.log(chalk.gray('   AI 服务初始化完成'));
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  AI 服务初始化失败: ${error.message}`));
      }
      this.options.aiEnabled = false;
    }
  }

  /**
   * 加载登录配置
   */
  async loadLoginConfig() {
    try {
      if (await fs.pathExists(this.options.configPath)) {
        this.loginConfig = await fs.readJson(this.options.configPath);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`   加载登录配置: ${this.options.configPath}`));
        }
        
        return true;
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  加载登录配置失败: ${error.message}`));
      }
    }
    
    return false;
  }

  /**
   * 保存登录配置
   */
  async saveLoginConfig(config) {
    try {
      await fs.writeJson(this.options.configPath, config, { spaces: 2 });
      
      if (this.options.verbose) {
        console.log(chalk.gray(`   保存登录配置: ${this.options.configPath}`));
      }
      
      return true;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  保存登录配置失败: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 尝试自动登录
   */
  async attemptLogin(page) {
    if (this.options.verbose) {
      console.log(chalk.blue('🔑 开始自动登录流程'));
    }

    // 0. 首先检查是否已经登录或不需要登录
    const loginStatus = await this.checkLoginStatus(page);
    if (loginStatus.isLoggedIn) {
      if (this.options.verbose) {
        console.log(chalk.green('✅ 已经登录或无需登录'));
      }
      this.isLoggedIn = true;
      return true;
    }

    if (!loginStatus.needsLogin) {
      if (this.options.verbose) {
        console.log(chalk.yellow('⚠️  页面不需要登录，跳过登录流程'));
      }
      this.isLoggedIn = true;
      return true;
    }

    // 1. 首先尝试默认登录逻辑
    const defaultLoginSuccess = await this.tryDefaultLogin(page);
    if (defaultLoginSuccess) {
      this.isLoggedIn = true;
      return true;
    }

    // 2. 如果默认登录失败，尝试使用已保存的 AI 配置
    if (await this.loadLoginConfig()) {
      const configLoginSuccess = await this.tryConfigLogin(page);
      if (configLoginSuccess) {
        this.isLoggedIn = true;
        return true;
      }
    }

    // 3. 如果都失败了，调用 AI 生成新的登录逻辑
    if (this.options.aiEnabled) {
      const aiLoginSuccess = await this.tryAIGeneratedLogin(page);
      if (aiLoginSuccess) {
        this.isLoggedIn = true;
        return true;
      }
    }

    if (this.options.verbose) {
      console.log(chalk.red('❌ 所有登录方式都失败了'));
    }

    return false;
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus(page) {
    try {
      const currentUrl = page.url();

      // 检查URL是否包含登录相关路径
      const loginPaths = ['/login', '/signin', '/auth', '/authentication'];
      const needsLogin = loginPaths.some(path => currentUrl.includes(path));

      if (!needsLogin) {
        return { isLoggedIn: true, needsLogin: false };
      }

      // 检查页面是否有登录表单
      const hasLoginForm = await page.evaluate(() => {
        // 检查常见的登录表单选择器
        const loginSelectors = [
          '.login-form',
          '.login-container',
          'form[name="login"]',
          'form[id="login"]',
          'input[name="username"]',
          'input[name="email"]',
          'input[type="email"]',
          'input[placeholder*="用户名"]',
          'input[placeholder*="邮箱"]',
          'input[placeholder*="username"]',
          'input[placeholder*="email"]'
        ];

        return loginSelectors.some(selector => {
          try {
            return document.querySelector(selector) !== null;
          } catch (e) {
            return false;
          }
        });
      });

      return {
        isLoggedIn: false,
        needsLogin: hasLoginForm,
        hasLoginForm
      };
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  检查登录状态失败: ${error.message}`));
      }
      // 默认假设需要登录
      return { isLoggedIn: false, needsLogin: true };
    }
  }

  /**
   * 尝试默认登录逻辑
   */
  async tryDefaultLogin(page) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray('   尝试默认登录逻辑...'));
      }

      // 智能查找登录表单
      const loginForm = await this.findLoginForm(page);
      if (!loginForm.found) {
        if (this.options.verbose) {
          console.log(chalk.yellow('   未找到登录表单'));
        }
        return false;
      }

      await this.sleep(1000);

      // 智能填写用户名
      const usernameSuccess = await this.fillUsernameField(page, loginForm.usernameSelector);
      if (!usernameSuccess) {
        if (this.options.verbose) {
          console.log(chalk.yellow('   无法填写用户名'));
        }
        return false;
      }

      // 智能填写密码
      const passwordSuccess = await this.fillPasswordField(page, loginForm.passwordSelector);
      if (!passwordSuccess) {
        if (this.options.verbose) {
          console.log(chalk.yellow('   无法填写密码'));
        }
        return false;
      }

      // 智能点击登录按钮
      const submitSuccess = await this.clickLoginButton(page, loginForm.submitSelector);
      if (!submitSuccess) {
        if (this.options.verbose) {
          console.log(chalk.yellow('   无法点击登录按钮'));
        }
        return false;
      }

      // 等待登录结果
      await this.sleep(3000);

      // 检查登录是否成功
      const success = await this.checkLoginSuccess(page);

      if (this.options.verbose) {
        console.log(chalk.gray(`   默认登录${success ? '成功' : '失败'}: ${page.url()}`));
      }

      return success;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   默认登录出错: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 智能查找登录表单
   */
  async findLoginForm(page) {
    try {
      const formInfo = await page.evaluate(() => {
        // 查找登录表单容器
        const formContainers = [
          '.login-form',
          '.login-container',
          '.auth-form',
          'form[name="login"]',
          'form[id="login"]',
          'form'
        ];

        let formContainer = null;
        for (const selector of formContainers) {
          const element = document.querySelector(selector);
          if (element) {
            formContainer = element;
            break;
          }
        }

        if (!formContainer) {
          return { found: false };
        }

        // 查找用户名输入框
        const usernameSelectors = [
          'input[name="username"]',
          'input[name="email"]',
          'input[type="email"]',
          'input[placeholder*="用户名"]',
          'input[placeholder*="邮箱"]',
          'input[placeholder*="username"]',
          'input[placeholder*="email"]',
          'input[id*="username"]',
          'input[id*="email"]'
        ];

        let usernameSelector = null;
        for (const selector of usernameSelectors) {
          if (formContainer.querySelector(selector) || document.querySelector(selector)) {
            usernameSelector = selector;
            break;
          }
        }

        // 查找密码输入框
        const passwordSelectors = [
          'input[name="password"]',
          'input[type="password"]',
          'input[placeholder*="密码"]',
          'input[placeholder*="password"]',
          'input[id*="password"]'
        ];

        let passwordSelector = null;
        for (const selector of passwordSelectors) {
          if (formContainer.querySelector(selector) || document.querySelector(selector)) {
            passwordSelector = selector;
            break;
          }
        }

        // 查找提交按钮
        const submitSelectors = [
          'button[type="submit"]',
          'input[type="submit"]',
          '.el-button--primary',
          '.btn-primary',
          '.login-btn',
          'button:contains("登录")',
          'button:contains("Login")',
          'button:contains("Sign In")'
        ];

        let submitSelector = null;
        for (const selector of submitSelectors) {
          if (formContainer.querySelector(selector) || document.querySelector(selector)) {
            submitSelector = selector;
            break;
          }
        }

        return {
          found: true,
          usernameSelector,
          passwordSelector,
          submitSelector
        };
      });

      return formInfo;
    } catch (error) {
      return { found: false };
    }
  }

  /**
   * 填写用户名字段
   */
  async fillUsernameField(page, selector) {
    if (!selector) return false;

    try {
      await page.waitForSelector(selector, { visible: true, timeout: 3000 });
      const input = await page.$(selector);
      if (input) {
        await input.click({ clickCount: 3 });
        await input.type(this.options.username, { delay: 100 });
        return true;
      }
    } catch (error) {
      // 尝试其他方法
    }

    return false;
  }

  /**
   * 填写密码字段
   */
  async fillPasswordField(page, selector) {
    if (!selector) return false;

    try {
      await page.waitForSelector(selector, { visible: true, timeout: 3000 });
      const input = await page.$(selector);
      if (input) {
        await input.click({ clickCount: 3 });
        await input.type(this.options.password, { delay: 100 });
        return true;
      }
    } catch (error) {
      // 尝试其他方法
    }

    return false;
  }

  /**
   * 点击登录按钮
   */
  async clickLoginButton(page, selector) {
    if (!selector) return false;

    try {
      const button = await page.$(selector);
      if (button) {
        await button.click();
        return true;
      }
    } catch (error) {
      // 尝试其他方法
    }

    return false;
  }

  /**
   * 检查登录是否成功
   */
  async checkLoginSuccess(page) {
    try {
      // 等待页面跳转
      await page.waitForFunction(
        () => !window.location.href.includes('/login') && !window.location.href.includes('/signin'),
        { timeout: 10000 }
      );
      return true;
    } catch (timeoutError) {
      // 检查当前URL
      const currentUrl = page.url();
      return !currentUrl.includes('/login') && !currentUrl.includes('/signin');
    }
  }

  /**
   * 尝试使用配置文件中的登录逻辑
   */
  async tryConfigLogin(page) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray('   尝试配置文件登录逻辑...'));
      }

      if (!this.loginConfig || !this.loginConfig.steps) {
        return false;
      }

      // 执行配置中的登录步骤
      for (const step of this.loginConfig.steps) {
        await this.executeLoginStep(page, step);
      }

      // 检查登录结果
      await this.sleep(3000);
      const currentUrl = page.url();
      const success = !currentUrl.includes('/login');

      if (this.options.verbose) {
        console.log(chalk.gray(`   配置登录${success ? '成功' : '失败'}: ${currentUrl}`));
      }

      return success;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   配置登录出错: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 尝试 AI 生成的登录逻辑
   */
  async tryAIGeneratedLogin(page) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray('   调用 AI 生成登录逻辑...'));
      }

      if (!this.aiService) {
        await this.initializeAI();
        if (!this.aiService) {
          return false;
        }
      }

      // 获取页面 DOM 结构
      const domStructure = await this.getLoginPageDOM(page);
      
      // 调用 AI 分析并生成登录步骤
      const loginSteps = await this.generateLoginSteps(domStructure);
      
      if (!loginSteps || loginSteps.length === 0) {
        return false;
      }

      // 执行 AI 生成的登录步骤
      for (const step of loginSteps) {
        await this.executeLoginStep(page, step);
      }

      // 检查登录结果
      await this.sleep(3000);
      const currentUrl = page.url();
      const success = !currentUrl.includes('/login');

      if (success) {
        // 保存成功的登录配置
        const config = {
          timestamp: new Date().toISOString(),
          username: this.options.username,
          steps: loginSteps,
          success: true
        };
        await this.saveLoginConfig(config);
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`   AI 登录${success ? '成功' : '失败'}: ${currentUrl}`));
      }

      return success;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   AI 登录出错: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 获取登录页面的 DOM 结构
   */
  async getLoginPageDOM(page) {
    try {
      const domStructure = await page.evaluate(() => {
        // 获取登录表单相关的 DOM 信息
        const forms = Array.from(document.querySelectorAll('form, .login-form, .login-container'));
        const inputs = Array.from(document.querySelectorAll('input'));
        const buttons = Array.from(document.querySelectorAll('button, .el-button, input[type="submit"]'));

        return {
          forms: forms.map(form => ({
            tagName: form.tagName,
            className: form.className,
            id: form.id,
            innerHTML: form.innerHTML.substring(0, 500) // 限制长度
          })),
          inputs: inputs.map(input => ({
            type: input.type,
            name: input.name,
            id: input.id,
            className: input.className,
            placeholder: input.placeholder,
            value: input.value
          })),
          buttons: buttons.map(button => ({
            tagName: button.tagName,
            type: button.type,
            className: button.className,
            id: button.id,
            textContent: button.textContent?.trim(),
            innerHTML: button.innerHTML
          })),
          url: window.location.href,
          title: document.title
        };
      });

      return domStructure;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   获取 DOM 结构失败: ${error.message}`));
      }
      return null;
    }
  }

  /**
   * 调用 AI 生成登录步骤
   */
  async generateLoginSteps(domStructure) {
    try {
      const prompt = this.buildLoginAnalysisPrompt(domStructure);

      const response = await this.aiService.callAI(prompt, {
        temperature: 0.1,
        maxTokens: 2000
      });

      // 解析 AI 响应，提取登录步骤
      const loginSteps = this.parseLoginStepsFromResponse(response);

      if (this.options.verbose) {
        console.log(chalk.gray(`   AI 生成了 ${loginSteps.length} 个登录步骤`));
      }

      return loginSteps;
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   AI 生成登录步骤失败: ${error.message}`));
      }
      return [];
    }
  }

  /**
   * 构建登录分析提示词
   */
  buildLoginAnalysisPrompt(domStructure) {
    return `
你是一个网页自动化专家，需要分析登录页面的 DOM 结构，生成 Puppeteer 可执行的登录步骤。

页面信息：
- URL: ${domStructure.url}
- 标题: ${domStructure.title}

表单信息：
${JSON.stringify(domStructure.forms, null, 2)}

输入框信息：
${JSON.stringify(domStructure.inputs, null, 2)}

按钮信息：
${JSON.stringify(domStructure.buttons, null, 2)}

用户凭据：
- 用户名: ${this.options.username}
- 密码: ${this.options.password}

请分析这个登录页面，生成一系列登录步骤。每个步骤应该包含以下信息：
- action: 操作类型 (wait, click, type, select)
- selector: CSS 选择器
- value: 输入值（如果需要）
- timeout: 超时时间（毫秒）

请以 JSON 格式返回步骤数组，例如：
\`\`\`json
[
  {
    "action": "wait",
    "selector": "input[name='username']",
    "timeout": 5000
  },
  {
    "action": "type",
    "selector": "input[name='username']",
    "value": "${this.options.username}"
  },
  {
    "action": "type",
    "selector": "input[name='password']",
    "value": "${this.options.password}"
  },
  {
    "action": "click",
    "selector": "button[type='submit']"
  }
]
\`\`\`

注意：
1. 确保选择器准确匹配页面元素
2. 包含必要的等待步骤
3. 考虑页面加载时间
4. 优先使用 name、id 等稳定的选择器
`;
  }

  /**
   * 从 AI 响应中解析登录步骤
   */
  parseLoginStepsFromResponse(response) {
    try {
      // 提取 JSON 代码块
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }

      // 尝试直接解析整个响应
      return JSON.parse(response);
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   解析 AI 响应失败: ${error.message}`));
        console.log(chalk.gray(`   AI 响应: ${response.substring(0, 200)}...`));
      }
      return [];
    }
  }

  /**
   * 执行单个登录步骤
   */
  async executeLoginStep(page, step) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`     执行步骤: ${step.action} ${step.selector}`));
      }

      switch (step.action) {
        case 'wait':
          await page.waitForSelector(step.selector, {
            timeout: step.timeout || 5000,
            visible: true
          });
          break;

        case 'click':
          await page.click(step.selector);
          if (step.delay) {
            await this.sleep(step.delay);
          }
          break;

        case 'type':
          // 先清空输入框
          await page.click(step.selector, { clickCount: 3 });
          await page.type(step.selector, step.value, { delay: step.delay || 100 });
          break;

        case 'select':
          await page.select(step.selector, step.value);
          break;

        case 'sleep':
          await this.sleep(step.duration || 1000);
          break;

        default:
          if (this.options.verbose) {
            console.log(chalk.yellow(`     未知步骤类型: ${step.action}`));
          }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`     步骤执行失败: ${error.message}`));
      }
      throw error;
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = AutoLoginManager;
